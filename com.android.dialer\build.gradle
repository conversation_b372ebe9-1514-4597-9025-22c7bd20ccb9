apply plugin: 'com.android.application'

android {
    namespace 'com.android.dialer'
    compileSdk 34

    defaultConfig {
        applicationId "com.android.dialer"
        minSdkVersion 24
        targetSdkVersion 34
        versionCode 150
        versionName "15.0"
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
        coreLibraryDesugaringEnabled true
    }
    

    
    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    lint {
        abortOnError false
        checkReleaseBuilds false
    }
}

dependencies {
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    testImplementation 'junit:junit:4.13.2'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation project(':com.android.common')
    implementation project(':com.android.phone.common')
    implementation(project(':com.android.contacts.common')) {
        exclude group: 'com.google.guava', module: 'guava'
    }
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.legacy:legacy-support-v13:1.0.0'
    implementation('com.umeng.analytics:analytics:latest.integration') {
        exclude group: 'com.google.guava', module: 'listenablefuture'
    }

    // Additional dependencies for Android 15 compatibility
    implementation 'androidx.core:core:1.12.0'
    implementation 'androidx.fragment:fragment:1.6.2'
    implementation 'androidx.activity:activity:1.9.1'
    implementation 'androidx.coordinatorlayout:coordinatorlayout:1.2.0'
    implementation 'androidx.annotation:annotation:1.8.2'
    
    // Add missing dependencies
    implementation 'com.google.guava:guava:31.1-android'
    implementation 'androidx.viewpager:viewpager:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'com.google.android.material:material:1.12.0'
    
    // Additional AndroidX dependencies for compatibility
    implementation 'androidx.viewpager2:viewpager2:1.0.0'
    implementation 'com.googlecode.libphonenumber:libphonenumber:8.13.27'
    implementation 'com.googlecode.libphonenumber:geocoder:2.214'
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.fragment:fragment-ktx:1.6.2'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    
    // AndroidX Loader for Fragment migration
    implementation 'androidx.loader:loader:1.1.0'
}

configurations {
    all {
        exclude group: 'com.google.guava', module: 'listenablefuture'
    }
}
