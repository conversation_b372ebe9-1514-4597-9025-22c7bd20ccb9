apply plugin: 'com.android.library'

android {
    namespace 'com.android.contacts.common'
    compileSdk 34

    defaultConfig {
        minSdkVersion 24
        targetSdkVersion 34
        versionCode 1
        versionName "1.0"
        
        multiDexEnabled true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
        coreLibraryDesugaringEnabled true
    }
    

    
    buildTypes {
        debug {
            minifyEnabled false
            debuggable true
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    lint {
        abortOnError false
        checkReleaseBuilds false
    }
    
    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
    }
}

dependencies {
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'
    implementation fileTree(include: ['*.jar'], dir: 'libs', exclude: ['guava.jar', 'libphonenumber.jar'])
    testImplementation 'junit:junit:4.13.2'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation project(':com.android.common')
    implementation project(':com.android.phone.common')
    implementation 'androidx.core:core:1.12.0'
    implementation 'androidx.localbroadcastmanager:localbroadcastmanager:1.1.0'
    
    // Use Android-optimized Guava instead of the JAR file
    implementation 'com.google.guava:guava:31.1-android'
    
    // Add libphonenumber dependency to fix compilation errors
    implementation 'com.googlecode.libphonenumber:libphonenumber:8.13.27'
    implementation 'com.googlecode.libphonenumber:geocoder:2.214'
}

configurations {
    all {
        exclude group: 'com.google.guava', module: 'listenablefuture'
    }
}
